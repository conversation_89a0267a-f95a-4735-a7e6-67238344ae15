import React from 'react';
import { useAuth } from '../contexts/AuthContext';

const AuthPage = () => {
  const { authenticateYouTube, authenticateFacebook, authStatus } = useAuth();

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-900">
      <div className="max-w-md w-full space-y-8 p-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-white mb-2">
            Stream Dashboard
          </h1>
          <p className="text-gray-400 mb-8">
            Connect your YouTube and Facebook accounts to start monitoring your live streams
          </p>
        </div>

        <div className="space-y-4">
          {/* YouTube Authentication */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <svg className="w-8 h-8 text-red-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136C4.495 20.455 12 20.455 12 20.455s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                </svg>
                <div>
                  <h3 className="text-lg font-semibold text-white">YouTube</h3>
                  <p className="text-sm text-gray-400">Monitor live chat and statistics</p>
                </div>
              </div>
              {authStatus.youtube && (
                <div className="text-green-400">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </div>
            
            {!authStatus.youtube ? (
              <button
                onClick={authenticateYouTube}
                className="auth-button youtube-auth w-full"
              >
                Connect YouTube
              </button>
            ) : (
              <div className="text-green-400 text-center py-2">
                ✓ Connected to YouTube
              </div>
            )}
          </div>

          {/* Facebook Authentication */}
          <div className="bg-gray-800 rounded-lg p-6 border border-gray-700">
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center">
                <svg className="w-8 h-8 text-blue-500 mr-3" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                </svg>
                <div>
                  <h3 className="text-lg font-semibold text-white">Facebook</h3>
                  <p className="text-sm text-gray-400">Monitor live comments and reactions</p>
                </div>
              </div>
              {authStatus.facebook && (
                <div className="text-green-400">
                  <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </div>
            
            {!authStatus.facebook ? (
              <button
                onClick={authenticateFacebook}
                className="auth-button facebook-auth w-full"
              >
                Connect Facebook
              </button>
            ) : (
              <div className="text-green-400 text-center py-2">
                ✓ Connected to Facebook
              </div>
            )}
          </div>
        </div>

        {authStatus.authenticated && (
          <div className="text-center pt-4">
            <p className="text-green-400 mb-4">
              ✓ Authentication complete! You can now access the dashboard.
            </p>
            <button
              onClick={() => window.location.reload()}
              className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-semibold transition-colors"
            >
              Go to Dashboard
            </button>
          </div>
        )}

        <div className="text-center text-sm text-gray-500 pt-6">
          <p>
            This dashboard monitors your live streams in real-time and can be used as an OBS browser source.
          </p>
          <p className="mt-2">
            Add <code className="bg-gray-800 px-2 py-1 rounded">?obs=true</code> to the URL for OBS overlay mode.
          </p>
        </div>
      </div>
    </div>
  );
};

export default AuthPage;
