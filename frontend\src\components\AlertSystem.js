import React from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useSocket } from '../contexts/SocketContext';

const AlertSystem = () => {
  const { alerts } = useSocket();

  const getAlertStyle = (alert) => {
    switch (alert.type) {
      case 'superchat':
        return 'superchat-alert';
      case 'facebook-star':
        return 'facebook-star-alert';
      default:
        return 'alert-popup';
    }
  };

  const getAlertIcon = (alert) => {
    switch (alert.type) {
      case 'superchat':
        return '💰';
      case 'facebook-star':
        return '⭐';
      case 'subscription':
        return '🔔';
      case 'donation':
        return '💝';
      default:
        return '🎉';
    }
  };

  const formatAlertTitle = (alert) => {
    switch (alert.type) {
      case 'superchat':
        return `Super Chat ${alert.amount}`;
      case 'facebook-star':
        return `Facebook Stars ${alert.amount}`;
      case 'subscription':
        return 'New Subscriber!';
      case 'donation':
        return `Donation ${alert.amount}`;
      default:
        return 'Alert';
    }
  };

  return (
    <div className="fixed top-4 right-4 z-50 space-y-4 max-w-sm">
      <AnimatePresence>
        {alerts.map((alert) => (
          <motion.div
            key={alert.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ 
              type: "spring", 
              stiffness: 300, 
              damping: 30 
            }}
            className={`${getAlertStyle(alert)} shadow-2xl`}
          >
            <div className="flex items-start space-x-3">
              <div className="text-3xl flex-shrink-0 animate-bounce">
                {getAlertIcon(alert)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="font-bold text-lg mb-1">
                  {formatAlertTitle(alert)}
                </div>
                
                <div className="font-semibold text-sm mb-2">
                  {alert.author}
                </div>
                
                {alert.message && (
                  <div className="text-sm opacity-90 break-words">
                    "{alert.message}"
                  </div>
                )}
                
                <div className="text-xs opacity-75 mt-2">
                  {new Date(alert.timestamp).toLocaleTimeString()}
                </div>
              </div>
            </div>
            
            {/* Animated progress bar */}
            <motion.div
              className="absolute bottom-0 left-0 h-1 bg-white bg-opacity-30 rounded-b-lg"
              initial={{ width: "100%" }}
              animate={{ width: "0%" }}
              transition={{ duration: 10, ease: "linear" }}
            />
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
};

export default AlertSystem;
