import React from 'react';

const ConnectionStatus = ({ connected }) => {
  if (connected) {
    return null; // Don't show anything when connected
  }

  return (
    <div className="bg-red-600 text-white p-2 text-center text-sm">
      <div className="flex items-center justify-center space-x-2">
        <div className="w-2 h-2 bg-white rounded-full animate-pulse"></div>
        <span>Disconnected from server - Attempting to reconnect...</span>
      </div>
    </div>
  );
};

export default ConnectionStatus;
