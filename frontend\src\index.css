@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-900 text-white font-sans;
  }
  
  * {
    @apply scrollbar-thin scrollbar-thumb-gray-600 scrollbar-track-gray-800;
  }
}

@layer components {
  .chat-container {
    @apply bg-gray-800 rounded-lg p-4 h-96 overflow-y-auto border border-gray-700;
  }
  
  .chat-message {
    @apply mb-3 p-2 rounded-lg bg-gray-700 animate-slide-up;
  }
  
  .youtube-message {
    @apply border-l-4 border-youtube-red;
  }
  
  .facebook-message {
    @apply border-l-4 border-facebook-blue;
  }
  
  .stat-card {
    @apply bg-gray-800 rounded-lg p-4 border border-gray-700 text-center;
  }
  
  .alert-popup {
    @apply fixed top-4 right-4 z-50 bg-gradient-to-r from-purple-600 to-pink-600 
           text-white p-4 rounded-lg shadow-lg animate-bounce-in max-w-sm;
  }
  
  .superchat-alert {
    @apply bg-gradient-to-r from-yellow-500 to-orange-500;
  }
  
  .facebook-star-alert {
    @apply bg-gradient-to-r from-blue-500 to-purple-500;
  }
  
  .auth-button {
    @apply px-6 py-3 rounded-lg font-semibold text-white transition-all duration-200 
           hover:scale-105 active:scale-95 shadow-lg;
  }
  
  .youtube-auth {
    @apply bg-youtube-red hover:bg-red-700;
  }
  
  .facebook-auth {
    @apply bg-facebook-blue hover:bg-blue-700;
  }
  
  .dashboard-grid {
    @apply grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6 p-6;
  }
  
  .obs-overlay {
    @apply bg-transparent;
  }

  .obs-overlay .chat-container,
  .obs-overlay .stat-card {
    @apply bg-black bg-opacity-70 backdrop-blur-sm border-opacity-50;
  }

  .obs-overlay .chat-message {
    @apply bg-gray-900 bg-opacity-80;
  }

  .obs-overlay h1,
  .obs-overlay h2,
  .obs-overlay h3 {
    @apply drop-shadow-lg;
  }

  /* OBS-specific responsive scaling */
  @media (max-width: 1920px) {
    .obs-overlay {
      @apply text-sm;
    }

    .obs-overlay .chat-container {
      @apply text-xs;
    }
  }

  @media (max-width: 1280px) {
    .obs-overlay {
      @apply text-xs;
    }

    .obs-overlay .stat-card {
      @apply p-2;
    }
  }
}

@layer utilities {
  .scrollbar-thin {
    scrollbar-width: thin;
  }
  
  .scrollbar-thumb-gray-600::-webkit-scrollbar-thumb {
    background-color: #4b5563;
    border-radius: 0.375rem;
  }
  
  .scrollbar-track-gray-800::-webkit-scrollbar-track {
    background-color: #1f2937;
  }
  
  .scrollbar-thin::-webkit-scrollbar {
    width: 6px;
  }
}
