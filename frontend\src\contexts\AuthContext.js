import React, { createContext, useContext, useEffect, useState } from 'react';
import axios from 'axios';

const AuthContext = createContext();

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [authStatus, setAuthStatus] = useState({
    youtube: false,
    facebook: false,
    authenticated: false,
    loading: true
  });

  const [streamStatus, setStreamStatus] = useState({
    monitoring: false,
    youtube: { activePolling: 0 },
    facebook: { activePolling: 0 }
  });

  const baseURL = process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000';

  // Configure axios defaults
  axios.defaults.withCredentials = true;

  const checkAuthStatus = async () => {
    try {
      const response = await axios.get(`${baseURL}/auth/status`);
      setAuthStatus({
        ...response.data,
        loading: false
      });
    } catch (error) {
      console.error('Auth status check failed:', error);
      setAuthStatus({
        youtube: false,
        facebook: false,
        authenticated: false,
        loading: false
      });
    }
  };

  const checkStreamStatus = async () => {
    try {
      const response = await axios.get(`${baseURL}/api/stream/status`);
      setStreamStatus({
        monitoring: response.data.youtube.activePolling > 0 || response.data.facebook.activePolling > 0,
        youtube: response.data.youtube,
        facebook: response.data.facebook
      });
    } catch (error) {
      console.error('Stream status check failed:', error);
    }
  };

  const startStreamMonitoring = async () => {
    try {
      const response = await axios.post(`${baseURL}/api/stream/start`);
      await checkStreamStatus();
      return response.data;
    } catch (error) {
      console.error('Failed to start stream monitoring:', error);
      throw error;
    }
  };

  const stopStreamMonitoring = async () => {
    try {
      const response = await axios.post(`${baseURL}/api/stream/stop`);
      await checkStreamStatus();
      return response.data;
    } catch (error) {
      console.error('Failed to stop stream monitoring:', error);
      throw error;
    }
  };

  const logout = async () => {
    try {
      await axios.post(`${baseURL}/auth/logout`);
      setAuthStatus({
        youtube: false,
        facebook: false,
        authenticated: false,
        loading: false
      });
      setStreamStatus({
        monitoring: false,
        youtube: { activePolling: 0 },
        facebook: { activePolling: 0 }
      });
    } catch (error) {
      console.error('Logout failed:', error);
    }
  };

  const authenticateYouTube = () => {
    window.location.href = `${baseURL}/auth/youtube`;
  };

  const authenticateFacebook = () => {
    window.location.href = `${baseURL}/auth/facebook`;
  };

  useEffect(() => {
    checkAuthStatus();
    
    // Check for authentication success/failure in URL params
    const urlParams = new URLSearchParams(window.location.search);
    const youtubeSuccess = urlParams.get('youtube') === 'success';
    const facebookSuccess = urlParams.get('facebook') === 'success';
    const authError = urlParams.get('error');

    if (youtubeSuccess || facebookSuccess || authError) {
      // Clean up URL
      window.history.replaceState({}, document.title, window.location.pathname);
      
      // Recheck auth status after a short delay
      setTimeout(checkAuthStatus, 1000);
    }
  }, []);

  useEffect(() => {
    if (authStatus.authenticated && !authStatus.loading) {
      checkStreamStatus();
    }
  }, [authStatus.authenticated, authStatus.loading]);

  const value = {
    authStatus,
    streamStatus,
    checkAuthStatus,
    checkStreamStatus,
    startStreamMonitoring,
    stopStreamMonitoring,
    logout,
    authenticateYouTube,
    authenticateFacebook
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
