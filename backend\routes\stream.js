const express = require('express');
const YouTubeService = require('../services/youtubeService');
const FacebookService = require('../services/facebookService');
const logger = require('../utils/logger');

const router = express.Router();

// Service instances
const youtubeService = new YouTubeService();
const facebookService = new FacebookService();

// Middleware to check authentication
const requireAuth = (req, res, next) => {
  if (!req.session.youtubeAuth && !req.session.facebookAuth) {
    return res.status(401).json({ error: 'Authentication required' });
  }
  next();
};

// Start monitoring streams
router.post('/start', requireAuth, async (req, res) => {
  try {
    const io = req.app.get('io');
    const results = {
      youtube: null,
      facebook: null,
      success: false
    };

    // Start YouTube monitoring if authenticated
    if (req.session.youtubeAuth) {
      try {
        const youtubeStreams = await youtubeService.startStreamMonitoring(
          io,
          req.session.youtubeAuth.accessToken,
          req.session.youtubeAuth.refreshToken
        );
        results.youtube = {
          success: true,
          streams: youtubeStreams.length,
          data: youtubeStreams
        };
        logger.info(`Started monitoring ${youtubeStreams.length} YouTube streams`);
      } catch (error) {
        logger.error('YouTube monitoring start failed:', error);
        results.youtube = {
          success: false,
          error: error.message
        };
      }
    }

    // Start Facebook monitoring if authenticated
    if (req.session.facebookAuth) {
      try {
        const facebookStreams = await facebookService.startStreamMonitoring(
          io,
          req.session.facebookAuth.accessToken
        );
        results.facebook = {
          success: true,
          streams: facebookStreams.length,
          data: facebookStreams
        };
        logger.info(`Started monitoring ${facebookStreams.length} Facebook streams`);
      } catch (error) {
        logger.error('Facebook monitoring start failed:', error);
        results.facebook = {
          success: false,
          error: error.message
        };
      }
    }

    results.success = (results.youtube?.success || results.facebook?.success);

    res.json(results);
  } catch (error) {
    logger.error('Stream monitoring start error:', error);
    res.status(500).json({ error: 'Failed to start stream monitoring' });
  }
});

// Stop monitoring streams
router.post('/stop', requireAuth, (req, res) => {
  try {
    youtubeService.stopAllPolling();
    facebookService.stopAllPolling();
    
    logger.info('Stopped all stream monitoring');
    res.json({ message: 'Stream monitoring stopped' });
  } catch (error) {
    logger.error('Stream monitoring stop error:', error);
    res.status(500).json({ error: 'Failed to stop stream monitoring' });
  }
});

// Get current stream status
router.get('/status', requireAuth, async (req, res) => {
  try {
    const status = {
      youtube: {
        authenticated: !!req.session.youtubeAuth,
        activePolling: youtubeService.chatPollingIntervals.size
      },
      facebook: {
        authenticated: !!req.session.facebookAuth,
        activePolling: facebookService.commentPollingIntervals.size
      }
    };

    res.json(status);
  } catch (error) {
    logger.error('Stream status error:', error);
    res.status(500).json({ error: 'Failed to get stream status' });
  }
});

// Get YouTube live streams
router.get('/youtube/streams', requireAuth, async (req, res) => {
  if (!req.session.youtubeAuth) {
    return res.status(401).json({ error: 'YouTube authentication required' });
  }

  try {
    youtubeService.setCredentials(
      req.session.youtubeAuth.accessToken,
      req.session.youtubeAuth.refreshToken
    );

    const streams = await youtubeService.getLiveStreams();
    res.json(streams);
  } catch (error) {
    logger.error('YouTube streams fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch YouTube streams' });
  }
});

// Get Facebook live videos
router.get('/facebook/streams', requireAuth, async (req, res) => {
  if (!req.session.facebookAuth) {
    return res.status(401).json({ error: 'Facebook authentication required' });
  }

  try {
    facebookService.setAccessToken(req.session.facebookAuth.accessToken);
    
    const pages = await facebookService.getPages();
    const allStreams = [];

    for (const page of pages) {
      const streams = await facebookService.getLiveVideos(page.id, page.access_token);
      allStreams.push(...streams.map(stream => ({
        ...stream,
        pageName: page.name,
        pageId: page.id
      })));
    }

    res.json(allStreams);
  } catch (error) {
    logger.error('Facebook streams fetch error:', error);
    res.status(500).json({ error: 'Failed to fetch Facebook streams' });
  }
});

// Test endpoint for WebSocket connection
router.post('/test-websocket', requireAuth, (req, res) => {
  const io = req.app.get('io');
  
  // Send test data to all connected clients
  io.to('dashboard').emit('test-message', {
    message: 'WebSocket connection test',
    timestamp: new Date().toISOString()
  });

  res.json({ message: 'Test message sent to WebSocket clients' });
});

module.exports = router;
