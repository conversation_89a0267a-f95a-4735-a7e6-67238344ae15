import React from 'react';
import { useSocket } from '../contexts/SocketContext';
import ChatPanel from './ChatPanel';
import StatsPanel from './StatsPanel';
import AlertSystem from './AlertSystem';

const OBSOverlay = () => {
  const { connected } = useSocket();

  return (
    <div className="min-h-screen bg-transparent obs-overlay">
      {/* Connection indicator for OBS */}
      {!connected && (
        <div className="fixed top-4 left-4 bg-red-600 bg-opacity-80 text-white px-3 py-1 rounded text-sm">
          Disconnected
        </div>
      )}

      {/* Compact layout for OBS */}
      <div className="p-4 grid grid-cols-1 lg:grid-cols-2 gap-4 max-w-6xl">
        {/* Chat sections - smaller for overlay */}
        <div className="space-y-4">
          <div>
            <h3 className="text-sm font-bold text-red-500 mb-2 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136C4.495 20.455 12 20.455 12 20.455s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
              </svg>
              YouTube
            </h3>
            <div className="h-64">
              <ChatPanel platform="youtube" />
            </div>
          </div>

          <div>
            <h3 className="text-sm font-bold text-blue-500 mb-2 flex items-center">
              <svg className="w-4 h-4 mr-1" fill="currentColor" viewBox="0 0 24 24">
                <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
              </svg>
              Facebook
            </h3>
            <div className="h-64">
              <ChatPanel platform="facebook" />
            </div>
          </div>
        </div>

        {/* Stats section - compact for overlay */}
        <div>
          <h3 className="text-sm font-bold text-green-500 mb-2 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Stats
          </h3>
          <div className="h-auto">
            <StatsPanel />
          </div>
        </div>
      </div>

      {/* Alert system - same as main dashboard */}
      <AlertSystem />
    </div>
  );
};

export default OBSOverlay;
