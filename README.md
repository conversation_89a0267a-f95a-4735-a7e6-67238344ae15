# 🎥 Stream Dashboard

A real-time streaming dashboard that displays live chat messages, viewer counts, likes, and alerts from both YouTube and Facebook live streams. Perfect for streamers who want to monitor multiple platforms simultaneously and can be used as an OBS browser source overlay.

## ✨ Features

- **Real-time Chat Display**: Live chat messages from YouTube and Facebook
- **Live Statistics**: Viewer counts, likes, and reactions from both platforms
- **Alert System**: Animated notifications for Super Chats, Facebook Stars, donations, and subscriptions
- **OBS Integration**: Optimized overlay mode for OBS Studio browser sources
- **OAuth2 Authentication**: Secure authentication with YouTube and Facebook APIs
- **WebSocket Communication**: Real-time data streaming with Socket.IO
- **Responsive Design**: Modern UI with Tailwind CSS and Framer Motion animations
- **Docker Support**: Easy deployment with Docker and Docker Compose

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   React Frontend │    │  Node.js Backend │    │  External APIs  │
│                 │    │                 │    │                 │
│ • Dashboard UI  │◄──►│ • Express Server│◄──►│ • YouTube API   │
│ • WebSocket     │    │ • Socket.IO     │    │ • Facebook API  │
│ • OBS Overlay   │    │ • OAuth2 Auth   │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 Quick Start

### Prerequisites

- Node.js 18+ and npm
- YouTube Data API v3 credentials
- Facebook Graph API credentials
- Docker (optional, for deployment)

### 1. Clone and Install

```bash
git clone <repository-url>
cd stream-dashboard
npm run install:all
```

### 2. Environment Setup

Copy the example environment file and configure it:

```bash
cp .env.example .env
```

Edit `.env` with your API credentials:

```env
# Server Configuration
PORT=5000
NODE_ENV=development

# YouTube API Configuration
YOUTUBE_CLIENT_ID=your_youtube_client_id_here
YOUTUBE_CLIENT_SECRET=your_youtube_client_secret_here
YOUTUBE_API_KEY=your_youtube_api_key_here

# Facebook API Configuration
FACEBOOK_APP_ID=your_facebook_app_id_here
FACEBOOK_APP_SECRET=your_facebook_app_secret_here

# Session Configuration
SESSION_SECRET=your_random_session_secret_here

# Frontend URL (for CORS)
FRONTEND_URL=http://localhost:3000
```

### 3. Run Development Server

```bash
npm run dev
```

This starts both backend (port 5000) and frontend (port 3000) servers.

## 🔑 API Setup Instructions

### YouTube Data API v3

1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable YouTube Data API v3
4. Create OAuth 2.0 credentials:
   - Application type: Web application
   - Authorized redirect URIs: `http://localhost:5000/auth/youtube/callback`
5. Copy Client ID and Client Secret to `.env`

### Facebook Graph API

1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Create a new app
3. Add Facebook Login product
4. Configure OAuth redirect URI: `http://localhost:5000/auth/facebook/callback`
5. Request permissions:
   - `pages_read_engagement`
   - `pages_show_list`
   - `pages_manage_posts`
   - `public_profile`
6. Copy App ID and App Secret to `.env`

## 📱 Usage

### Dashboard Access

1. Open http://localhost:3000
2. Authenticate with YouTube and/or Facebook
3. Click "Start Monitoring" to begin real-time data collection
4. View live chat, statistics, and alerts in real-time

### OBS Browser Source

1. Add Browser Source in OBS
2. Set URL to: `http://localhost:3000/?obs=true`
3. Set Width: 1920, Height: 1080
4. Enable "Shutdown source when not visible" for performance
5. The overlay will show transparent background with chat and alerts

## 🐳 Docker Deployment

### Development

```bash
docker-compose up -d
```

### Production

```bash
# Build and deploy
chmod +x deploy.sh
./deploy.sh

# With nginx reverse proxy
docker-compose --profile production up -d
```

## 📁 Project Structure

```
stream-dashboard/
├── backend/                 # Node.js backend
│   ├── config/             # Passport OAuth configuration
│   ├── routes/             # API routes
│   ├── services/           # YouTube & Facebook services
│   ├── utils/              # Utilities (logger, etc.)
│   └── server.js           # Main server file
├── frontend/               # React frontend
│   ├── public/             # Static files
│   ├── src/
│   │   ├── components/     # React components
│   │   ├── contexts/       # React contexts (Auth, Socket)
│   │   └── App.js          # Main app component
├── docker-compose.yml      # Docker composition
├── Dockerfile             # Docker build configuration
├── nginx.conf             # Nginx configuration
└── deploy.sh              # Deployment script
```

## 🔧 Development

### Backend Development

```bash
cd backend
npm run dev  # Starts with nodemon for auto-reload
```

### Frontend Development

```bash
cd frontend
npm start    # Starts React development server
```

### Testing WebSocket Connection

Use the test endpoint to verify WebSocket functionality:

```bash
curl -X POST http://localhost:5000/api/stream/test-websocket
```

## 🎨 Customization

### Styling

- Edit `frontend/src/index.css` for global styles
- Modify Tailwind configuration in `frontend/tailwind.config.js`
- Customize component styles in individual component files

### Alert Animations

- Modify `frontend/src/components/AlertSystem.js`
- Add new alert types in the backend services
- Customize animation timing and effects

### OBS Overlay Layout

- Edit `frontend/src/components/OBSOverlay.js`
- Adjust sizing and positioning for your stream layout
- Modify transparency and background effects

## 🚨 Troubleshooting

### Common Issues

1. **Authentication Fails**
   - Verify API credentials in `.env`
   - Check redirect URIs match exactly
   - Ensure APIs are enabled in respective consoles

2. **WebSocket Connection Issues**
   - Check CORS configuration
   - Verify backend server is running
   - Check browser console for connection errors

3. **No Live Streams Found**
   - Ensure you have active live streams
   - Verify API permissions are granted
   - Check API rate limits

### Logs

```bash
# View application logs
docker-compose logs -f

# View specific service logs
docker-compose logs -f stream-dashboard
```

## 📄 License

MIT License - see LICENSE file for details.

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📞 Support

For issues and questions:
- Check the troubleshooting section
- Review API documentation
- Create an issue in the repository
