{"name": "stream-dashboard-backend", "version": "1.0.0", "description": "Backend API for streaming dashboard", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js"}, "dependencies": {"express": "^4.18.2", "socket.io": "^4.7.5", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.2", "googleapis": "^128.0.0", "passport": "^0.7.0", "passport-google-oauth20": "^2.0.0", "passport-facebook": "^3.0.0", "express-session": "^1.17.3", "helmet": "^7.1.0", "express-rate-limit": "^7.1.5", "winston": "^3.11.0"}, "devDependencies": {"nodemon": "^3.0.2"}}