const passport = require('passport');
const GoogleStrategy = require('passport-google-oauth20').Strategy;
const FacebookStrategy = require('passport-facebook').Strategy;
const logger = require('../utils/logger');

// Serialize user for session
passport.serializeUser((user, done) => {
  done(null, user);
});

// Deserialize user from session
passport.deserializeUser((user, done) => {
  done(null, user);
});

// Google OAuth Strategy
passport.use(new GoogleStrategy({
  clientID: process.env.YOUTUBE_CLIENT_ID,
  clientSecret: process.env.YOUTUBE_CLIENT_SECRET,
  callbackURL: process.env.YOUTUBE_CALLBACK_URL || '/auth/youtube/callback',
  scope: [
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/youtube.force-ssl'
  ]
}, async (accessToken, refreshToken, profile, done) => {
  try {
    const user = {
      id: profile.id,
      provider: 'youtube',
      displayName: profile.displayName,
      accessToken: accessToken,
      refreshToken: refreshToken,
      profile: profile
    };
    
    logger.info(`YouTube authentication successful for user: ${profile.displayName}`);
    return done(null, user);
  } catch (error) {
    logger.error('YouTube authentication error:', error);
    return done(error, null);
  }
}));

// Facebook OAuth Strategy
passport.use(new FacebookStrategy({
  clientID: process.env.FACEBOOK_APP_ID,
  clientSecret: process.env.FACEBOOK_APP_SECRET,
  callbackURL: process.env.FACEBOOK_CALLBACK_URL || '/auth/facebook/callback',
  profileFields: ['id', 'displayName', 'email'],
  scope: [
    'pages_read_engagement',
    'pages_show_list',
    'pages_manage_posts',
    'public_profile'
  ]
}, async (accessToken, refreshToken, profile, done) => {
  try {
    const user = {
      id: profile.id,
      provider: 'facebook',
      displayName: profile.displayName,
      accessToken: accessToken,
      refreshToken: refreshToken,
      profile: profile
    };
    
    logger.info(`Facebook authentication successful for user: ${profile.displayName}`);
    return done(null, user);
  } catch (error) {
    logger.error('Facebook authentication error:', error);
    return done(error, null);
  }
}));

module.exports = passport;
