const axios = require('axios');
const logger = require('../utils/logger');

class FacebookService {
  constructor() {
    this.baseURL = 'https://graph.facebook.com/v18.0';
    this.accessToken = null;
    this.liveVideoPollingIntervals = new Map();
    this.commentPollingIntervals = new Map();
  }

  setAccessToken(accessToken) {
    this.accessToken = accessToken;
  }

  async makeRequest(endpoint, params = {}) {
    try {
      const response = await axios.get(`${this.baseURL}${endpoint}`, {
        params: {
          access_token: this.accessToken,
          ...params
        }
      });
      return response.data;
    } catch (error) {
      logger.error('Facebook API request error:', error.response?.data || error.message);
      throw error;
    }
  }

  async getPages() {
    try {
      const data = await this.makeRequest('/me/accounts', {
        fields: 'id,name,access_token'
      });
      return data.data || [];
    } catch (error) {
      logger.error('Error fetching Facebook pages:', error);
      throw error;
    }
  }

  async getLiveVideos(pageId, pageAccessToken) {
    try {
      const data = await this.makeRequest(`/${pageId}/live_videos`, {
        access_token: pageAccessToken,
        fields: 'id,title,description,status,live_views,permalink_url,created_time'
      });
      return data.data || [];
    } catch (error) {
      logger.error('Error fetching Facebook live videos:', error);
      throw error;
    }
  }

  async getLiveVideoComments(liveVideoId, pageAccessToken, since = null) {
    try {
      const params = {
        access_token: pageAccessToken,
        fields: 'id,message,created_time,from{id,name,picture},like_count,comment_count',
        order: 'chronological',
        limit: 100
      };

      if (since) {
        params.since = since;
      }

      const data = await this.makeRequest(`/${liveVideoId}/comments`, params);
      return data;
    } catch (error) {
      logger.error('Error fetching Facebook live video comments:', error);
      throw error;
    }
  }

  async getLiveVideoReactions(liveVideoId, pageAccessToken) {
    try {
      const data = await this.makeRequest(`/${liveVideoId}/reactions`, {
        access_token: pageAccessToken,
        summary: 'total_count',
        fields: 'type'
      });

      // Count reactions by type
      const reactionCounts = {
        like: 0,
        love: 0,
        wow: 0,
        haha: 0,
        sad: 0,
        angry: 0,
        total: data.summary?.total_count || 0
      };

      if (data.data) {
        data.data.forEach(reaction => {
          if (reactionCounts.hasOwnProperty(reaction.type.toLowerCase())) {
            reactionCounts[reaction.type.toLowerCase()]++;
          }
        });
      }

      return reactionCounts;
    } catch (error) {
      logger.error('Error fetching Facebook live video reactions:', error);
      throw error;
    }
  }

  async getLiveVideoStats(liveVideoId, pageAccessToken) {
    try {
      const data = await this.makeRequest(`/${liveVideoId}`, {
        access_token: pageAccessToken,
        fields: 'live_views,status,title,description'
      });
      return data;
    } catch (error) {
      logger.error('Error fetching Facebook live video stats:', error);
      throw error;
    }
  }

  startCommentPolling(liveVideoId, pageAccessToken, io) {
    if (this.commentPollingIntervals.has(liveVideoId)) {
      return; // Already polling this video
    }

    let lastCommentTime = Math.floor(Date.now() / 1000);

    const pollComments = async () => {
      try {
        const commentsData = await this.getLiveVideoComments(
          liveVideoId, 
          pageAccessToken, 
          lastCommentTime
        );

        if (commentsData.data && commentsData.data.length > 0) {
          const comments = commentsData.data.map(comment => ({
            id: comment.id,
            type: 'facebook',
            author: {
              name: comment.from.name,
              id: comment.from.id,
              profileImageUrl: comment.from.picture?.data?.url
            },
            message: {
              text: comment.message,
              publishedAt: comment.created_time,
              likeCount: comment.like_count || 0,
              commentCount: comment.comment_count || 0
            }
          }));

          // Emit comments to connected clients
          io.to('dashboard').emit('facebook-chat-messages', comments);

          // Update last comment time
          const latestComment = commentsData.data[commentsData.data.length - 1];
          lastCommentTime = Math.floor(new Date(latestComment.created_time).getTime() / 1000);
        }

      } catch (error) {
        logger.error('Error polling Facebook comments:', error);
      }
    };

    // Initial poll
    pollComments();

    // Set up interval polling (every 5 seconds)
    const intervalId = setInterval(pollComments, 5000);
    this.commentPollingIntervals.set(liveVideoId, intervalId);

    logger.info(`Started Facebook comment polling for video: ${liveVideoId}`);
  }

  startStatsPolling(liveVideoId, pageAccessToken, io) {
    const pollStats = async () => {
      try {
        const [stats, reactions] = await Promise.all([
          this.getLiveVideoStats(liveVideoId, pageAccessToken),
          this.getLiveVideoReactions(liveVideoId, pageAccessToken)
        ]);

        io.to('dashboard').emit('facebook-stats', {
          videoId: liveVideoId,
          liveViews: stats.live_views || 0,
          status: stats.status,
          reactions: reactions
        });

      } catch (error) {
        logger.error('Error polling Facebook stats:', error);
      }
    };

    // Poll stats every 30 seconds
    setInterval(pollStats, 30000);
    pollStats(); // Initial poll
  }

  stopCommentPolling(liveVideoId) {
    const intervalId = this.commentPollingIntervals.get(liveVideoId);
    if (intervalId) {
      clearInterval(intervalId);
      this.commentPollingIntervals.delete(liveVideoId);
      logger.info(`Stopped Facebook comment polling for video: ${liveVideoId}`);
    }
  }

  async startStreamMonitoring(io, accessToken) {
    this.setAccessToken(accessToken);

    try {
      const pages = await this.getPages();
      const allLiveVideos = [];

      for (const page of pages) {
        const liveVideos = await this.getLiveVideos(page.id, page.access_token);
        
        for (const video of liveVideos) {
          if (video.status === 'LIVE') {
            allLiveVideos.push({
              ...video,
              pageId: page.id,
              pageName: page.name,
              pageAccessToken: page.access_token
            });

            // Start polling for this live video
            this.startCommentPolling(video.id, page.access_token, io);
            this.startStatsPolling(video.id, page.access_token, io);
          }
        }
      }

      return allLiveVideos;
    } catch (error) {
      logger.error('Error starting Facebook stream monitoring:', error);
      throw error;
    }
  }

  stopAllPolling() {
    this.commentPollingIntervals.forEach((intervalId, videoId) => {
      clearInterval(intervalId);
    });
    this.commentPollingIntervals.clear();
    logger.info('Stopped all Facebook polling');
  }
}

module.exports = FacebookService;
