import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';

const Header = () => {
  const { 
    authStatus, 
    streamStatus, 
    startStreamMonitoring, 
    stopStreamMonitoring, 
    logout 
  } = useAuth();
  const { clearMessages, clearAlerts } = useSocket();

  const handleStartMonitoring = async () => {
    try {
      await startStreamMonitoring();
    } catch (error) {
      console.error('Failed to start monitoring:', error);
    }
  };

  const handleStopMonitoring = async () => {
    try {
      await stopStreamMonitoring();
    } catch (error) {
      console.error('Failed to stop monitoring:', error);
    }
  };

  const handleClearData = () => {
    clearMessages();
    clearAlerts();
  };

  return (
    <header className="bg-gray-800 border-b border-gray-700 p-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-white">
            Stream Dashboard
          </h1>
          
          {/* Authentication Status */}
          <div className="flex items-center space-x-2">
            {authStatus.youtube && (
              <span className="px-2 py-1 bg-red-600 text-white text-xs rounded-full">
                YouTube
              </span>
            )}
            {authStatus.facebook && (
              <span className="px-2 py-1 bg-blue-600 text-white text-xs rounded-full">
                Facebook
              </span>
            )}
          </div>
        </div>

        <div className="flex items-center space-x-4">
          {/* Monitoring Status */}
          <div className="text-sm text-gray-400">
            {streamStatus.monitoring ? (
              <span className="flex items-center text-green-400">
                <div className="w-2 h-2 bg-green-400 rounded-full mr-2 animate-pulse"></div>
                Monitoring Active
              </span>
            ) : (
              <span className="flex items-center text-gray-500">
                <div className="w-2 h-2 bg-gray-500 rounded-full mr-2"></div>
                Monitoring Stopped
              </span>
            )}
          </div>

          {/* Control Buttons */}
          <div className="flex items-center space-x-2">
            {!streamStatus.monitoring ? (
              <button
                onClick={handleStartMonitoring}
                className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
              >
                Start Monitoring
              </button>
            ) : (
              <button
                onClick={handleStopMonitoring}
                className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded-lg transition-colors"
              >
                Stop Monitoring
              </button>
            )}

            <button
              onClick={handleClearData}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Clear Data
            </button>

            <button
              onClick={logout}
              className="px-4 py-2 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
            >
              Logout
            </button>
          </div>
        </div>
      </div>

      {/* Stream Status Details */}
      {streamStatus.monitoring && (
        <div className="mt-4 flex items-center space-x-6 text-sm text-gray-400">
          <div>
            YouTube Streams: {streamStatus.youtube.activePolling}
          </div>
          <div>
            Facebook Streams: {streamStatus.facebook.activePolling}
          </div>
        </div>
      )}
    </header>
  );
};

export default Header;
