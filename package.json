{"name": "stream-dashboard", "version": "1.0.0", "description": "Real-time streaming dashboard for YouTube and Facebook live streams", "main": "index.js", "scripts": {"dev": "concurrently \"npm run server:dev\" \"npm run client:dev\"", "server:dev": "cd backend && npm run dev", "client:dev": "cd frontend && npm start", "build": "cd frontend && npm run build", "start": "cd backend && npm start", "install:all": "npm install && cd backend && npm install && cd ../frontend && npm install"}, "keywords": ["streaming", "dashboard", "youtube", "facebook", "real-time"], "author": "Stream Dashboard", "license": "MIT", "devDependencies": {"concurrently": "^8.2.2"}}