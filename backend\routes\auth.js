const express = require('express');
const passport = require('../config/passport');
const logger = require('../utils/logger');

const router = express.Router();

// YouTube authentication routes
router.get('/youtube', passport.authenticate('google', {
  scope: [
    'https://www.googleapis.com/auth/youtube.readonly',
    'https://www.googleapis.com/auth/youtube.force-ssl'
  ]
}));

router.get('/youtube/callback', 
  passport.authenticate('google', { failureRedirect: '/auth/failure' }),
  (req, res) => {
    // Store YouTube credentials in session
    req.session.youtubeAuth = {
      accessToken: req.user.accessToken,
      refreshToken: req.user.refreshToken,
      profile: req.user.profile
    };
    
    logger.info('YouTube authentication successful');
    res.redirect(`${process.env.FRONTEND_URL}?youtube=success`);
  }
);

// Facebook authentication routes
router.get('/facebook', passport.authenticate('facebook', {
  scope: [
    'pages_read_engagement',
    'pages_show_list',
    'pages_manage_posts',
    'public_profile'
  ]
}));

router.get('/facebook/callback',
  passport.authenticate('facebook', { failureRedirect: '/auth/failure' }),
  (req, res) => {
    // Store Facebook credentials in session
    req.session.facebookAuth = {
      accessToken: req.user.accessToken,
      refreshToken: req.user.refreshToken,
      profile: req.user.profile
    };
    
    logger.info('Facebook authentication successful');
    res.redirect(`${process.env.FRONTEND_URL}?facebook=success`);
  }
);

// Authentication status endpoint
router.get('/status', (req, res) => {
  const status = {
    youtube: !!req.session.youtubeAuth,
    facebook: !!req.session.facebookAuth,
    authenticated: !!(req.session.youtubeAuth || req.session.facebookAuth)
  };
  
  res.json(status);
});

// Logout endpoint
router.post('/logout', (req, res) => {
  req.session.destroy((err) => {
    if (err) {
      logger.error('Logout error:', err);
      return res.status(500).json({ error: 'Logout failed' });
    }
    
    res.json({ message: 'Logged out successfully' });
  });
});

// Authentication failure handler
router.get('/failure', (req, res) => {
  res.redirect(`${process.env.FRONTEND_URL}?error=auth_failed`);
});

module.exports = router;
