import React from 'react';
import { useSocket } from '../contexts/SocketContext';

const StatsPanel = () => {
  const { youtubeStats, facebookStats } = useSocket();

  const StatCard = ({ title, value, icon, color, subtitle }) => (
    <div className={`stat-card border-l-4 ${color}`}>
      <div className="flex items-center justify-between">
        <div>
          <p className="text-sm text-gray-400">{title}</p>
          <p className="text-2xl font-bold text-white">{value || '0'}</p>
          {subtitle && (
            <p className="text-xs text-gray-500 mt-1">{subtitle}</p>
          )}
        </div>
        <div className="text-3xl opacity-50">
          {icon}
        </div>
      </div>
    </div>
  );

  const formatNumber = (num) => {
    if (!num) return '0';
    const number = parseInt(num);
    if (number >= 1000000) {
      return (number / 1000000).toFixed(1) + 'M';
    }
    if (number >= 1000) {
      return (number / 1000).toFixed(1) + 'K';
    }
    return number.toLocaleString();
  };

  return (
    <div className="space-y-6">
      {/* YouTube Statistics */}
      <div>
        <h3 className="text-lg font-semibold text-red-500 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136C4.495 20.455 12 20.455 12 20.455s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
          </svg>
          YouTube Stats
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <StatCard
            title="Live Viewers"
            value={formatNumber(youtubeStats.concurrentViewers)}
            icon="👥"
            color="border-red-500"
            subtitle="Currently watching"
          />
          
          <StatCard
            title="Total Views"
            value={formatNumber(youtubeStats.viewCount)}
            icon="👁️"
            color="border-red-500"
            subtitle="All time"
          />
          
          <StatCard
            title="Likes"
            value={formatNumber(youtubeStats.likeCount)}
            icon="👍"
            color="border-red-500"
            subtitle="Current stream"
          />
        </div>
      </div>

      {/* Facebook Statistics */}
      <div>
        <h3 className="text-lg font-semibold text-blue-500 mb-4 flex items-center">
          <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
          Facebook Stats
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <StatCard
            title="Live Viewers"
            value={formatNumber(facebookStats.liveViews)}
            icon="👥"
            color="border-blue-500"
            subtitle="Currently watching"
          />
          
          <StatCard
            title="Total Reactions"
            value={formatNumber(facebookStats.reactions?.total)}
            icon="❤️"
            color="border-blue-500"
            subtitle="All reactions"
          />
        </div>

        {/* Facebook Reactions Breakdown */}
        {facebookStats.reactions && (
          <div className="stat-card">
            <h4 className="text-sm font-semibold text-gray-300 mb-3">Reaction Breakdown</h4>
            <div className="grid grid-cols-3 gap-2 text-center">
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">👍</div>
                <div className="text-sm text-gray-400">Like</div>
                <div className="font-semibold">{facebookStats.reactions.like || 0}</div>
              </div>
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">❤️</div>
                <div className="text-sm text-gray-400">Love</div>
                <div className="font-semibold">{facebookStats.reactions.love || 0}</div>
              </div>
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">😮</div>
                <div className="text-sm text-gray-400">Wow</div>
                <div className="font-semibold">{facebookStats.reactions.wow || 0}</div>
              </div>
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">😂</div>
                <div className="text-sm text-gray-400">Haha</div>
                <div className="font-semibold">{facebookStats.reactions.haha || 0}</div>
              </div>
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">😢</div>
                <div className="text-sm text-gray-400">Sad</div>
                <div className="font-semibold">{facebookStats.reactions.sad || 0}</div>
              </div>
              <div className="bg-gray-700 rounded p-2">
                <div className="text-lg">😠</div>
                <div className="text-sm text-gray-400">Angry</div>
                <div className="font-semibold">{facebookStats.reactions.angry || 0}</div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default StatsPanel;
