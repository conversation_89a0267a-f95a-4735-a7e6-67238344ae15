import React, { useState, useEffect } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Dashboard from './components/Dashboard';
import AuthPage from './components/AuthPage';
import OBSOverlay from './components/OBSOverlay';
import { SocketProvider } from './contexts/SocketContext';
import { AuthProvider } from './contexts/AuthContext';
import './index.css';

function App() {
  const [isOBSMode, setIsOBSMode] = useState(false);

  useEffect(() => {
    // Check if running in OBS mode (query parameter)
    const urlParams = new URLSearchParams(window.location.search);
    const obsMode = urlParams.get('obs') === 'true';
    setIsOBSMode(obsMode);

    // Apply OBS-specific styles
    if (obsMode) {
      document.body.classList.add('obs-overlay');
    }
  }, []);

  return (
    <AuthProvider>
      <SocketProvider>
        <Router>
          <div className="App min-h-screen">
            <Routes>
              <Route 
                path="/" 
                element={isOBSMode ? <OBSOverlay /> : <Dashboard />} 
              />
              <Route path="/auth" element={<AuthPage />} />
              <Route path="/obs" element={<OBSOverlay />} />
            </Routes>
          </div>
        </Router>
      </SocketProvider>
    </AuthProvider>
  );
}

export default App;
