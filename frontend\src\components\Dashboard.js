import React from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useSocket } from '../contexts/SocketContext';
import AuthPage from './AuthPage';
import Header from './Header';
import ChatPanel from './ChatPanel';
import StatsPanel from './StatsPanel';
import AlertSystem from './AlertSystem';
import ConnectionStatus from './ConnectionStatus';

const Dashboard = () => {
  const { authStatus } = useAuth();
  const { connected } = useSocket();

  if (authStatus.loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
          <p className="text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  if (!authStatus.authenticated) {
    return <AuthPage />;
  }

  return (
    <div className="min-h-screen bg-gray-900">
      <Header />
      <ConnectionStatus connected={connected} />
      
      <div className="dashboard-grid">
        {/* YouTube Chat Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-red-500 flex items-center">
            <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136C4.495 20.455 12 20.455 12 20.455s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
            </svg>
            YouTube Chat
          </h2>
          <ChatPanel platform="youtube" />
        </div>

        {/* Facebook Chat Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-blue-500 flex items-center">
            <svg className="w-6 h-6 mr-2" fill="currentColor" viewBox="0 0 24 24">
              <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
            </svg>
            Facebook Chat
          </h2>
          <ChatPanel platform="facebook" />
        </div>

        {/* Statistics Section */}
        <div className="space-y-4">
          <h2 className="text-xl font-bold text-green-500 flex items-center">
            <svg className="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
            </svg>
            Live Statistics
          </h2>
          <StatsPanel />
        </div>
      </div>

      {/* Alert System */}
      <AlertSystem />
    </div>
  );
};

export default Dashboard;
