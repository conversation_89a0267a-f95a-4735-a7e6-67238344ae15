import React, { useEffect, useRef } from 'react';
import { useSocket } from '../contexts/SocketContext';

const ChatPanel = ({ platform }) => {
  const { youtubeMessages, facebookMessages } = useSocket();
  const chatEndRef = useRef(null);
  
  const messages = platform === 'youtube' ? youtubeMessages : facebookMessages;

  useEffect(() => {
    // Auto-scroll to bottom when new messages arrive
    chatEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const formatTime = (timestamp) => {
    return new Date(timestamp).toLocaleTimeString('en-US', {
      hour12: false,
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const renderMessage = (message) => {
    const isYouTube = platform === 'youtube';
    const messageClass = isYouTube ? 'youtube-message' : 'facebook-message';
    
    return (
      <div key={message.id} className={`chat-message ${messageClass}`}>
        <div className="flex items-start space-x-3">
          {/* Profile Image */}
          <img
            src={message.author.profileImageUrl || '/default-avatar.png'}
            alt={message.author.name}
            className="w-8 h-8 rounded-full flex-shrink-0"
            onError={(e) => {
              e.target.src = '/default-avatar.png';
            }}
          />
          
          <div className="flex-1 min-w-0">
            {/* Author and Time */}
            <div className="flex items-center space-x-2 mb-1">
              <span className="font-semibold text-sm text-white truncate">
                {message.author.name}
              </span>
              
              {/* YouTube-specific badges */}
              {isYouTube && (
                <>
                  {message.author.isChatOwner && (
                    <span className="bg-red-600 text-white text-xs px-1 rounded">
                      OWNER
                    </span>
                  )}
                  {message.author.isChatModerator && (
                    <span className="bg-blue-600 text-white text-xs px-1 rounded">
                      MOD
                    </span>
                  )}
                  {message.author.isChatSponsor && (
                    <span className="bg-green-600 text-white text-xs px-1 rounded">
                      MEMBER
                    </span>
                  )}
                </>
              )}
              
              <span className="text-xs text-gray-400">
                {formatTime(message.message.publishedAt)}
              </span>
            </div>
            
            {/* Message Content */}
            <div className="text-sm text-gray-200">
              {/* Super Chat/Super Sticker for YouTube */}
              {isYouTube && message.message.superChatDetails && (
                <div className="bg-gradient-to-r from-yellow-500 to-orange-500 p-2 rounded mb-2">
                  <div className="font-bold text-white">
                    Super Chat: {message.message.superChatDetails.amountDisplayString}
                  </div>
                </div>
              )}
              
              {isYouTube && message.message.superStickerDetails && (
                <div className="bg-gradient-to-r from-purple-500 to-pink-500 p-2 rounded mb-2">
                  <div className="font-bold text-white">
                    Super Sticker: {message.message.superStickerDetails.amountDisplayString}
                  </div>
                </div>
              )}
              
              {/* Message Text */}
              <p className="break-words">{message.message.text}</p>
              
              {/* Facebook-specific stats */}
              {!isYouTube && (
                <div className="flex items-center space-x-4 mt-1 text-xs text-gray-400">
                  {message.message.likeCount > 0 && (
                    <span>👍 {message.message.likeCount}</span>
                  )}
                  {message.message.commentCount > 0 && (
                    <span>💬 {message.message.commentCount}</span>
                  )}
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="chat-container">
      {messages.length === 0 ? (
        <div className="flex items-center justify-center h-full text-gray-400">
          <div className="text-center">
            <div className="text-4xl mb-2">
              {platform === 'youtube' ? '📺' : '📘'}
            </div>
            <p>No {platform === 'youtube' ? 'YouTube' : 'Facebook'} messages yet</p>
            <p className="text-sm mt-1">Start monitoring to see live chat</p>
          </div>
        </div>
      ) : (
        <div className="space-y-2">
          {messages.map(renderMessage)}
          <div ref={chatEndRef} />
        </div>
      )}
    </div>
  );
};

export default ChatPanel;
