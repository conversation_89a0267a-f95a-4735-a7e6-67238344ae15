import React, { createContext, useContext, useEffect, useState } from 'react';
import io from 'socket.io-client';

const SocketContext = createContext();

export const useSocket = () => {
  const context = useContext(SocketContext);
  if (!context) {
    throw new Error('useSocket must be used within a SocketProvider');
  }
  return context;
};

export const SocketProvider = ({ children }) => {
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);
  const [youtubeMessages, setYoutubeMessages] = useState([]);
  const [facebookMessages, setFacebookMessages] = useState([]);
  const [youtubeStats, setYoutubeStats] = useState({});
  const [facebookStats, setFacebookStats] = useState({});
  const [alerts, setAlerts] = useState([]);

  useEffect(() => {
    // Initialize socket connection
    const newSocket = io(process.env.REACT_APP_BACKEND_URL || 'http://localhost:5000', {
      withCredentials: true
    });

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
      newSocket.emit('join-dashboard');
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    // YouTube event listeners
    newSocket.on('youtube-chat-messages', (messages) => {
      setYoutubeMessages(prev => [...prev, ...messages].slice(-100)); // Keep last 100 messages
    });

    newSocket.on('youtube-stats', (stats) => {
      setYoutubeStats(stats);
    });

    newSocket.on('youtube-alert', (alert) => {
      const alertWithId = { ...alert, id: Date.now() + Math.random() };
      setAlerts(prev => [...prev, alertWithId]);
      
      // Remove alert after 10 seconds
      setTimeout(() => {
        setAlerts(prev => prev.filter(a => a.id !== alertWithId.id));
      }, 10000);
    });

    // Facebook event listeners
    newSocket.on('facebook-chat-messages', (messages) => {
      setFacebookMessages(prev => [...prev, ...messages].slice(-100)); // Keep last 100 messages
    });

    newSocket.on('facebook-stats', (stats) => {
      setFacebookStats(stats);
    });

    newSocket.on('facebook-alert', (alert) => {
      const alertWithId = { ...alert, id: Date.now() + Math.random() };
      setAlerts(prev => [...prev, alertWithId]);
      
      // Remove alert after 10 seconds
      setTimeout(() => {
        setAlerts(prev => prev.filter(a => a.id !== alertWithId.id));
      }, 10000);
    });

    // Test message listener
    newSocket.on('test-message', (data) => {
      console.log('Test message received:', data);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, []);

  const clearMessages = () => {
    setYoutubeMessages([]);
    setFacebookMessages([]);
  };

  const clearAlerts = () => {
    setAlerts([]);
  };

  const value = {
    socket,
    connected,
    youtubeMessages,
    facebookMessages,
    youtubeStats,
    facebookStats,
    alerts,
    clearMessages,
    clearAlerts
  };

  return (
    <SocketContext.Provider value={value}>
      {children}
    </SocketContext.Provider>
  );
};
