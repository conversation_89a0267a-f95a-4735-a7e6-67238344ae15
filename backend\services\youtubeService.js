const { google } = require('googleapis');
const logger = require('../utils/logger');

class YouTubeService {
  constructor() {
    this.youtube = google.youtube('v3');
    this.oauth2Client = new google.auth.OAuth2(
      process.env.YOUTUBE_CLIENT_ID,
      process.env.YOUTUBE_CLIENT_SECRET,
      process.env.YOUTUBE_CALLBACK_URL
    );
    this.activeLiveStreams = new Map();
    this.chatPollingIntervals = new Map();
  }

  setCredentials(accessToken, refreshToken) {
    this.oauth2Client.setCredentials({
      access_token: accessToken,
      refresh_token: refreshToken
    });
  }

  async getLiveStreams() {
    try {
      const response = await this.youtube.liveBroadcasts.list({
        auth: this.oauth2Client,
        part: ['id', 'snippet', 'status', 'statistics'],
        broadcastStatus: 'active',
        mine: true
      });

      return response.data.items || [];
    } catch (error) {
      logger.error('Error fetching live streams:', error);
      throw error;
    }
  }

  async getLiveChatId(broadcastId) {
    try {
      const response = await this.youtube.liveBroadcasts.list({
        auth: this.oauth2Client,
        part: ['snippet'],
        id: [broadcastId]
      });

      const broadcast = response.data.items[0];
      return broadcast?.snippet?.liveChatId;
    } catch (error) {
      logger.error('Error fetching live chat ID:', error);
      throw error;
    }
  }

  async getLiveChatMessages(liveChatId, pageToken = null) {
    try {
      const params = {
        auth: this.oauth2Client,
        liveChatId: liveChatId,
        part: ['id', 'snippet', 'authorDetails']
      };

      if (pageToken) {
        params.pageToken = pageToken;
      }

      const response = await this.youtube.liveChatMessages.list(params);
      return response.data;
    } catch (error) {
      logger.error('Error fetching live chat messages:', error);
      throw error;
    }
  }

  async getVideoStatistics(videoId) {
    try {
      const response = await this.youtube.videos.list({
        auth: this.oauth2Client,
        part: ['statistics', 'liveStreamingDetails'],
        id: [videoId]
      });

      const video = response.data.items[0];
      return {
        viewCount: video?.statistics?.viewCount || '0',
        likeCount: video?.statistics?.likeCount || '0',
        concurrentViewers: video?.liveStreamingDetails?.concurrentViewers || '0'
      };
    } catch (error) {
      logger.error('Error fetching video statistics:', error);
      throw error;
    }
  }

  startChatPolling(broadcastId, liveChatId, io) {
    if (this.chatPollingIntervals.has(broadcastId)) {
      return; // Already polling this stream
    }

    let nextPageToken = null;
    let pollingIntervalMs = 5000; // Start with 5 seconds

    const pollChat = async () => {
      try {
        const chatData = await this.getLiveChatMessages(liveChatId, nextPageToken);
        
        if (chatData.items && chatData.items.length > 0) {
          const messages = chatData.items.map(item => ({
            id: item.id,
            type: 'youtube',
            author: {
              name: item.authorDetails.displayName,
              channelId: item.authorDetails.channelId,
              profileImageUrl: item.authorDetails.profileImageUrl,
              isChatOwner: item.authorDetails.isChatOwner,
              isChatSponsor: item.authorDetails.isChatSponsor,
              isChatModerator: item.authorDetails.isChatModerator
            },
            message: {
              text: item.snippet.displayMessage,
              publishedAt: item.snippet.publishedAt,
              type: item.snippet.type,
              superChatDetails: item.snippet.superChatDetails,
              superStickerDetails: item.snippet.superStickerDetails
            }
          }));

          // Emit messages to connected clients
          io.to('dashboard').emit('youtube-chat-messages', messages);

          // Check for super chats and emit alerts
          messages.forEach(msg => {
            if (msg.message.superChatDetails) {
              io.to('dashboard').emit('youtube-alert', {
                type: 'superchat',
                author: msg.author.name,
                amount: msg.message.superChatDetails.amountDisplayString,
                message: msg.message.text,
                timestamp: msg.message.publishedAt
              });
            }
          });
        }

        nextPageToken = chatData.nextPageToken;
        pollingIntervalMs = chatData.pollingIntervalMillis || 5000;

      } catch (error) {
        logger.error('Error polling YouTube chat:', error);
        pollingIntervalMs = Math.min(pollingIntervalMs * 2, 30000); // Exponential backoff
      }
    };

    // Initial poll
    pollChat();

    // Set up interval polling
    const intervalId = setInterval(pollChat, pollingIntervalMs);
    this.chatPollingIntervals.set(broadcastId, intervalId);

    logger.info(`Started YouTube chat polling for broadcast: ${broadcastId}`);
  }

  stopChatPolling(broadcastId) {
    const intervalId = this.chatPollingIntervals.get(broadcastId);
    if (intervalId) {
      clearInterval(intervalId);
      this.chatPollingIntervals.delete(broadcastId);
      logger.info(`Stopped YouTube chat polling for broadcast: ${broadcastId}`);
    }
  }

  async startStreamMonitoring(io, accessToken, refreshToken) {
    this.setCredentials(accessToken, refreshToken);

    try {
      const liveStreams = await this.getLiveStreams();
      
      for (const stream of liveStreams) {
        const liveChatId = await this.getLiveChatId(stream.id);
        
        if (liveChatId) {
          this.startChatPolling(stream.id, liveChatId, io);
          
          // Start statistics polling
          this.startStatisticsPolling(stream.snippet.resourceId?.videoId || stream.id, io);
        }
      }

      return liveStreams;
    } catch (error) {
      logger.error('Error starting YouTube stream monitoring:', error);
      throw error;
    }
  }

  startStatisticsPolling(videoId, io) {
    const pollStats = async () => {
      try {
        const stats = await this.getVideoStatistics(videoId);
        io.to('dashboard').emit('youtube-stats', {
          videoId,
          ...stats
        });
      } catch (error) {
        logger.error('Error polling YouTube statistics:', error);
      }
    };

    // Poll statistics every 30 seconds
    setInterval(pollStats, 30000);
    pollStats(); // Initial poll
  }

  stopAllPolling() {
    this.chatPollingIntervals.forEach((intervalId, broadcastId) => {
      clearInterval(intervalId);
    });
    this.chatPollingIntervals.clear();
    logger.info('Stopped all YouTube polling');
  }
}

module.exports = YouTubeService;
